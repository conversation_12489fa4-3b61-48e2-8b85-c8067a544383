<script setup>
import { ref, markRaw, onMounted, onUnmounted, computed, nextTick } from 'vue'
import SidebarMenu from './components/SidebarMenu.vue'
import PersistentPageContainer from './components/PersistentPageContainer.vue'
import CaptureScreen from './views/CaptureScreen.vue'
import SnakeGame from './components/SnakeGame.vue'
import EasyVoiceWebTTS from './components/EasyVoiceWebTTS.vue'
import VoiceCloning from './components/VoiceCloning.vue'
import ToastNotification from './components/ToastNotification.vue'
import HotkeySettings from './components/HotkeySettings.vue'

import { initializeTheme } from './utils/themeManager.js'
import { notificationService } from './utils/notificationService.js'
import { pageStateManager } from './utils/pageStateManager.js'

// 持久化页面组件 - 这些页面会保持状态
const persistentPages = [
  'Workbench', 'CmdScripts', 'JsScripts', 'PythonScripts', 
  'ToolsWorkspace', 'ScheduledTasks', 'Settings',
  // 工具管理下的页面也需要后台运行
  'EasyVoiceWebTTS', 'VoiceCloning', 'SnakeGame'
]

// 非持久化页面组件 - 这些页面每次都重新加载
const nonPersistentComponents = {
  CaptureScreen: markRaw(CaptureScreen),
  Hotkeys: markRaw(HotkeySettings)
}

const activeComponentName = ref('Workbench')
const persistentPageContainerRef = ref(null)
const showDebugInfo = ref(false) // 开发模式下显示调试信息
const toastNotificationRef = ref(null)
const isReloading = ref(false) // 热重载状态

// 计算属性
const isPersistentPage = computed(() => {
  return persistentPages.includes(activeComponentName.value)
})

const activeComponent = computed(() => {
  return nonPersistentComponents[activeComponentName.value]
})

const isDev = computed(() => {
  return import.meta.env.DEV
})

// 处理菜单点击
function handleMenuClick(menuName, isToolsMenu = false, toolConfig = {}) {
  console.log(`[App] 菜单点击: ${menuName}, 工具菜单: ${isToolsMenu}`)
  console.log(`[App] 当前activeComponentName: ${activeComponentName.value}`)
  console.log(`[App] 持久化页面列表:`, persistentPages)
  console.log(`[App] 是否为持久化页面: ${persistentPages.includes(menuName)}`)
  console.log(`[App] isPersistentPage计算值: ${isPersistentPage.value}`)
  
  // 直接处理所有页面导航，不区分工具菜单
  if (persistentPages.includes(menuName)) {
    // Handle persistent pages - use persistent container
    console.log(`[App] 设置持久化页面: ${menuName}`)
    activeComponentName.value = menuName
    sessionStorage.setItem('activeMenu', menuName)
    window.location.hash = `#${menuName}`
  } else {
    // Handle non-persistent pages - direct component display
    console.log(`[App] 设置非持久化页面: ${menuName}`)
    activeComponentName.value = menuName
    sessionStorage.setItem('activeMenu', menuName)
    window.location.hash = `#${menuName}`
  }
  
  console.log(`[App] 页面切换后activeComponentName: ${activeComponentName.value}`)
  console.log(`[App] 页面切换后isPersistentPage: ${isPersistentPage.value}`)
  
  // 强制触发响应式更新
  nextTick(() => {
    console.log(`[App] nextTick后activeComponentName: ${activeComponentName.value}`)
    console.log(`[App] nextTick后isPersistentPage: ${isPersistentPage.value}`)
  })
}

// 方法
const handleNavigateTo = (componentName) => {
  console.log(`[App] 导航到: ${componentName}`)
  activeComponentName.value = componentName
  sessionStorage.setItem('activeMenu', componentName)
  window.location.hash = `#${componentName}`
}

const handlePageLoaded = (event) => {
  console.log(`[App] 页面加载完成:`, event)
}

const handlePageError = (event) => {
  console.error(`[App] 页面加载错误:`, event)
  // 可以在这里显示错误通知
  if (toastNotificationRef.value) {
    toastNotificationRef.value.show({
      type: 'error',
      title: '页面加载失败',
      message: `无法加载页面 ${event.pageId}: ${event.error}`
    })
  }
}

const resetPageStates = () => {
  if (persistentPageContainerRef.value) {
    persistentPageContainerRef.value.reset()
    // 显示成功通知
    if (toastNotificationRef.value) {
      toastNotificationRef.value.show({
        type: 'success',
        title: '重置完成',
        message: '所有页面状态已重置'
      })
    }
  }
}

// 热重载功能
const reloadApp = () => {
  if (isReloading.value) return // 防止重复点击

  console.log('热重载Vue项目...')
  isReloading.value = true

  // 显示重载提示
  showReloadIndicator()

  // 延迟执行重载，让用户看到视觉反馈
  setTimeout(() => {
    // 重载当前页面但保持在同一个路由
    window.location.reload()
  }, 800)
}

const showReloadIndicator = () => {
  // 创建重载指示器
  const indicator = document.createElement('div')
  indicator.className = 'reload-indicator'
  indicator.innerHTML = `
    <div class="reload-content">
      <div class="reload-spinner">🔄</div>
      <div class="reload-text">正在重载Vue项目...</div>
    </div>
  `

  // 添加样式
  const style = document.createElement('style')
  style.textContent = `
    .reload-indicator {
      position: fixed;
      top: 0;
      left: 0;
      width: 100vw;
      height: 100vh;
      background: rgba(26, 26, 26, 0.95);
      display: flex;
      justify-content: center;
      align-items: center;
      z-index: 10000;
      backdrop-filter: blur(5px);
    }

    .reload-content {
      text-align: center;
      color: var(--text-primary);
      font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    }

    .reload-spinner {
      font-size: 48px;
      animation: spin 1s linear infinite;
      margin-bottom: 16px;
    }

    .reload-text {
      font-size: 16px;
      font-weight: 500;
      color: #e0e0e0;
    }

    @keyframes spin {
      from { transform: rotate(0deg); }
      to { transform: rotate(360deg); }
    }
  `

  document.head.appendChild(style)
  document.body.appendChild(indicator)
}

// 处理URL hash导航
const handleHashChange = () => {
  const hash = window.location.hash.slice(1) // 移除 # 符号
  if (hash && (persistentPages.includes(hash) || nonPersistentComponents[hash])) {
    activeComponentName.value = hash
  }
}

// 生命周期
onMounted(async () => {
  // 初始化主题系统
  await initializeTheme()

  // 初始化页面状态管理器
  pageStateManager.initialize()

  // 初始化通知服务
  if (toastNotificationRef.value) {
    notificationService.initialize(toastNotificationRef.value)
  }

  // 监听hash变化
  window.addEventListener('hashchange', handleHashChange)

  // 初始化时检查hash
  if (window.location.hash) {
    handleHashChange()
  } else {
    // 恢复上次选择的菜单
    const savedComponent = sessionStorage.getItem('activeMenu')
    if (savedComponent && (persistentPages.includes(savedComponent) || nonPersistentComponents[savedComponent])) {
      activeComponentName.value = savedComponent
    }
  }

  // 开发模式下显示调试信息
  if (isDev.value) {
    console.log('[App] 开发模式已启用，可以显示调试信息')
  }
})

onUnmounted(() => {
  window.removeEventListener('hashchange', handleHashChange)
  
  // 保存页面状态
  pageStateManager.savePersistedState()
})
</script>

<template>
  <div class="app-container">
    <SidebarMenu @menu-click="handleMenuClick" :active-menu="activeComponentName" />
    <main class="main-content">
      <!-- 持久化页面容器 -->
      <PersistentPageContainer
        v-if="isPersistentPage"
        ref="persistentPageContainerRef"
        :current-page="activeComponentName"
        :show-debug-info="showDebugInfo"
        @navigate-to="handleNavigateTo"
        @page-loaded="handlePageLoaded"
        @page-error="handlePageError"
      />
      
      <!-- 非持久化页面 -->
      <component
        v-else
        :is="activeComponent"
        @navigate-to="handleNavigateTo"
      />
    </main>

    <!-- Toast Notifications -->
    <ToastNotification ref="toastNotificationRef" />
    
    <!-- 调试控制 (开发模式) -->
    <div v-if="isDev" class="debug-controls">
      <button @click="showDebugInfo = !showDebugInfo" class="debug-btn">
        {{ showDebugInfo ? '隐藏' : '显示' }}调试信息
      </button>
      <button @click="reloadApp" class="debug-btn reload" title="热重载Vue项目" :disabled="isReloading">
        <span class="reload-icon" :class="{ 'spinning': isReloading }">🔄</span>
        {{ isReloading ? '重载中...' : '热重载' }}
      </button>
    </div>
  </div>
</template>

<style scoped>
.app-container {
  display: flex;
  height: 100vh;
  width: 100vw;
  background-color: var(--background);
  margin: 0;
  padding: 0;
  overflow: hidden; /* 保持主容器不滚动 */
  transition: background-color var(--transition-normal);
}

.main-content {
  flex: 1;
  overflow-y: auto; /* 允许垂直滚动 */
  overflow-x: hidden; /* 防止水平滚动 */
  padding: 0;
  margin: 0;
  background-color: var(--background);
  transition: background-color var(--transition-normal);
  height: 100vh; /* 确保高度固定 */
  position: relative;
}

.debug-controls {
  position: fixed;
  bottom: 20px;
  left: 20px;
  display: flex;
  flex-direction: row;
  gap: 12px;
  z-index: 1000;
}

.debug-btn {
  background: var(--primary);
  color: white;
  border: none;
  padding: 8px 12px;
  border-radius: 6px;
  font-size: 12px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  white-space: nowrap;
}

.debug-btn:hover {
  background: var(--primary-dark);
  transform: translateY(-1px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
}

.debug-btn:active {
  transform: translateY(0);
}

.debug-btn.reload {
  background: #28a745;
}

.debug-btn.reload:hover {
  background: #218838;
}

.debug-btn.reload:disabled {
  background: #6c757d;
  cursor: not-allowed;
  transform: none;
}

.reload-icon {
  transition: transform 0.3s ease;
}

.reload-icon.spinning {
  animation: spin 1s linear infinite;
}

@keyframes spin {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}
</style>