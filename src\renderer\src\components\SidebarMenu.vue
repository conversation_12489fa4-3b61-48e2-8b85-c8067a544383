<template>
  <div class="sidebar-menu">
    <div class="menu-header">
      <div class="logo-section">
        <img :src="logoSrc" alt="Logo" class="logo" />
        <span class="app-title">清风系统</span>
      </div>
    </div>

    <div class="menu-content">
      <ul>
        <li
          v-for="item in menuItems"
          :key="item.name"
          :class="{
            active: props.activeMenu === item.name || (item.children && item.children.some(child => child.name === props.activeMenu)),
            'has-children': item.children,
            expanded: item.children && expandedMenus.includes(item.name)
          }"
          @click="handleMenuClick(item)"
        >
          <div class="menu-item-content">
            <span class="icon">{{ item.icon }}</span>
            <span class="text">{{ item.label }}</span>
            <span v-if="item.children" class="expand-icon">
              {{ expandedMenus.includes(item.name) ? '▼' : '▶' }}
            </span>
          </div>

          <!-- 二级菜单 -->
          <ul v-if="item.children && expandedMenus.includes(item.name)" class="submenu">
            <li
              v-for="child in item.children"
              :key="child.name"
              :class="{ active: props.activeMenu === child.name }"
              @click.stop="activateSubmenu(child.name, item.name)"
            >
              <span class="icon">{{ child.icon }}</span>
              <span class="text">{{ child.label }}</span>
            </li>
          </ul>
        </li>
      </ul>
    </div>

    <div class="menu-footer">
      <div class="reload-section">
        <button
          class="reload-btn"
          :disabled="isReloading"
          @click="reloadApp"
        >
          <span :class="['reload-icon', { spinning: isReloading }]">🔄</span>
          {{ isReloading ? '重载中...' : '热重载' }}
        </button>
      </div>
      <div class="footer-info-line">
        <span class="version-info">v{{ appVersion }}</span>
        <span class="time-info">{{ currentTime }}</span>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, onUnmounted } from 'vue'
import logoImage from '../assets/qingfeng.png'

const props = defineProps({
  activeMenu: {
    type: String,
    required: true
  }
})

const emit = defineEmits(['menu-click'])

const currentTime = ref('')
const currentDate = ref('')
const expandedMenus = ref([]) // 默认折叠所有菜单
const isReloading = ref(false)
const logoSrc = ref(logoImage)
const appVersion = ref('1.0.0')

const menuItems = computed(() => [
  { name: 'Workbench', label: '工作台', icon: '💼' },

  {
    name: 'ScriptManagement',
    label: '脚本管理',
    icon: '📁',
    children: [
      { name: 'CmdScripts', label: 'CMD脚本', icon: '📜' },
      { name: 'JsScripts', label: 'JS脚本', icon: '📄' },
      { name: 'PythonScripts', label: 'Python脚本', icon: '🐍' }
    ]
  },
  {
      name: 'ToolsManagement',
      label: '工具管理',
      icon: '🔧',
      children: [
        { name: 'EasyVoiceWebTTS', label: '文本转语音', icon: '🗣️' },
        { name: 'VoiceCloning', label: '语音克隆', icon: '🎭' },
        { name: 'SnakeGame', label: '贪吃蛇游戏', icon: '🐍' }
      ]
    },
  { name: 'ScheduledTasks', label: '定时任务', icon: '⏰' },
  { name: 'Hotkeys', label: '快捷键', icon: '⌨️' },
  { name: 'Settings', label: '设置', icon: '⚙️' }
])

function handleMenuClick(item) {
  if (item.children) {
    // 切换展开/收起状态
    const index = expandedMenus.value.indexOf(item.name)
    if (index > -1) {
      expandedMenus.value.splice(index, 1)
    } else {
      expandedMenus.value.push(item.name)
    }

    // 工具管理菜单点击时也导航到工具管理页面
    if (item.name === 'ToolsManagement') {
      activateMenu(item.name)
    }
    // 其他有子菜单的项目可以根据需要导航到对应页面
  } else {
    activateMenu(item.name)
  }
}

function activateMenu(name) {
  console.log(`[SidebarMenu] 激活菜单: ${name}`)
  emit('menu-click', name, false, {})
}

function activateSubmenu(childName, parentName) {
  console.log(`[SidebarMenu] 激活子菜单: ${childName}, 父菜单: ${parentName}`)
  console.log(`[SidebarMenu] 准备发出menu-click事件`)
  
  // 直接导航到子菜单页面
  emit('menu-click', childName, false, {})
  
  console.log(`[SidebarMenu] menu-click事件已发出: ${childName}`)
}

function reloadApp() {
  if (isReloading.value) return // 防止重复点击

  console.log('热重载Vue项目...')
  isReloading.value = true

  // 显示重载提示
  showReloadIndicator()

  // 延迟执行重载，让用户看到视觉反馈
  setTimeout(() => {
    // 重载当前页面但保持在同一个路由
    window.location.reload()
  }, 800)
}

function showReloadIndicator() {
  // 创建重载指示器
  const indicator = document.createElement('div')
  indicator.className = 'reload-indicator'
  indicator.innerHTML = `
    <div class="reload-content">
      <div class="reload-spinner">🔄</div>
      <div class="reload-text">正在重载Vue项目...</div>
    </div>
  `

  // 添加样式
  const style = document.createElement('style')
  style.textContent = `
    .reload-indicator {
      position: fixed;
      top: 0;
      left: 0;
      width: 100vw;
      height: 100vh;
      background: rgba(26, 26, 26, 0.95);
      display: flex;
      justify-content: center;
      align-items: center;
      z-index: 10000;
      backdrop-filter: blur(5px);
    }

    .reload-content {
      text-align: center;
      color: var(--text-primary);
      font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    }

    .reload-spinner {
      font-size: 48px;
      animation: spin 1s linear infinite;
      margin-bottom: 16px;
    }

    .reload-text {
      font-size: 16px;
      font-weight: 500;
      color: #e0e0e0;
    }

    @keyframes spin {
      from { transform: rotate(0deg); }
      to { transform: rotate(360deg); }
    }
  `

  document.head.appendChild(style)
  document.body.appendChild(indicator)
}

function updateTime() {
  const now = new Date()
  currentTime.value = now.toLocaleTimeString('zh-CN', {
    hour12: false,
    hour: '2-digit',
    minute: '2-digit',
    second: '2-digit'
  })
  currentDate.value = now.toLocaleDateString('zh-CN', {
    month: '2-digit',
    day: '2-digit'
  })
}

// 获取应用版本号
import { safeApiCall } from '../utils/apiCheck.js'
async function getAppVersion() {
  try {
    // 使用API检查工具安全调用
    const result = await safeApiCall('getAppInfo', { success: true, version: '1.0.0-dev' })
    if (result.success) {
      appVersion.value = result.version
    } else {
      appVersion.value = '1.0.0-dev'
    }
  } catch (error) {
    console.error('Failed to get app version:', error)
    appVersion.value = '1.0.0-dev'
  }
}

let timeInterval = null

onMounted(() => {
  updateTime()
  getAppVersion()
  timeInterval = setInterval(updateTime, 1000)
})

onUnmounted(() => {
  if (timeInterval) {
    clearInterval(timeInterval)
  }
})
</script>

<style scoped>
.sidebar-menu {
  width: 240px;
  background-color: var(--sidebar-background);
  height: 100%;
  display: flex;
  flex-direction: column;
  border-right: 1px solid var(--border);
  transition: background-color var(--transition-normal), border-color var(--transition-normal);
}

.menu-header {
  padding: 16px;
  border-bottom: 1px solid var(--border);
  background-color: var(--sidebar-header-background);
  transition: background-color var(--transition-normal), border-color var(--transition-normal);
}

.logo-section {
  display: flex;
  align-items: center;
  gap: 12px;
}

.logo {
  width: 24px;
  height: 24px;
  object-fit: contain;
  margin-left: 40px;
}

.app-title {
  font-size: 25px;
  font-weight: 600;
  color: var(--text-primary);
  letter-spacing: 0.5px;
  transition: color var(--transition-normal);
}

.menu-content {
  flex: 1;
  padding: 16px 10px;
  overflow-y: auto;
}

.menu-content::-webkit-scrollbar {
  width: 4px;
}

.menu-content::-webkit-scrollbar-track {
  background: transparent;
}

.menu-content::-webkit-scrollbar-thumb {
  background: var(--border);
  border-radius: 2px;
  transition: background-color var(--transition-fast);
}

.menu-content::-webkit-scrollbar-thumb:hover {
  background: var(--border-secondary);
}

ul {
  list-style-type: none;
  padding: 0;
  margin: 0;
}

li {
  margin-bottom: 4px;
  font-size: 15px;
  color: var(--text-tertiary);
  transition: color var(--transition-normal);
}

.menu-item-content {
  display: flex;
  align-items: center;
  padding: 12px 15px;
  border-radius: 8px;
  transition: all var(--transition-fast);
  cursor: pointer;
}

li.active > .menu-item-content {
  background-color: var(--sidebar-item-active);
  color: var(--text-primary);
}

li:not(.active) > .menu-item-content:hover {
  background-color: var(--sidebar-item-hover);
}

li.has-children > .menu-item-content {
  justify-content: space-between;
}

.icon {
  margin-right: 12px;
  font-size: 18px;
}

.text {
  flex: 1;
  font-weight: 500;
}

.expand-icon {
  font-size: 12px;
  transition: transform var(--transition-fast);
}

.submenu {
  margin-top: 4px;
  margin-left: 20px;
  padding-left: 15px;
  border-left: 1px solid var(--border);
  transition: border-color var(--transition-normal);
}

.submenu li {
  padding: 8px 10px;
  border-radius: 6px;
  transition: all var(--transition-fast);
  cursor: pointer;
  display: flex;
  align-items: center;
}

.submenu li .icon {
  font-size: 16px;
}

.submenu li.active {
  background-color: var(--sidebar-item-active);
  color: var(--text-primary);
}

.submenu li:not(.active):hover {
  background-color: var(--sidebar-item-hover);
}

.menu-footer {
  padding: 16px;
  border-top: 1px solid var(--border);
  transition: border-color var(--transition-normal);
  background: linear-gradient(135deg, var(--sidebar-background) 0%, var(--background-secondary) 100%);
}

.reload-section {
  margin-bottom: 16px;
}

.reload-btn {
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  padding: 12px 16px;
  border: 1px solid var(--border);
  background: linear-gradient(135deg, var(--primary) 0%, var(--primary-dark) 100%);
  color: white;
  border-radius: 8px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all var(--transition-normal);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.reload-btn:hover:not(:disabled) {
  background: linear-gradient(135deg, var(--primary-light) 0%, var(--primary) 100%);
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
}

.reload-btn:active:not(:disabled) {
  transform: translateY(0);
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.15);
}

.reload-btn:disabled {
  background: var(--background-tertiary);
  border-color: var(--border-secondary);
  color: var(--text-disabled);
  cursor: not-allowed;
  transform: none;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  opacity: 0.6;
}

.reload-icon {
  margin-right: 8px;
  font-size: 14px;
  transition: transform 0.3s ease;
}

.reload-icon.spinning {
  animation: spin 1s linear infinite;
}

@keyframes spin {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

.footer-info-line {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 10px 12px;
  font-size: 12px;
  color: var(--text-tertiary);
  border-radius: 6px;
  background: var(--card-background);
  border: 1px solid var(--border);
  transition: all var(--transition-normal);
}

.version-info {
  font-weight: 600;
  color: var(--primary);
}

.time-info {
  font-family: 'Courier New', monospace;
  color: var(--text-secondary);
}
</style>