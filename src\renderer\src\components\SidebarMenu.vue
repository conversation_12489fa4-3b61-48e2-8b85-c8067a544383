<template>
  <div class="sidebar-menu">
    <div class="menu-header">
      <div class="logo-section">
        <img :src="logoSrc" alt="Logo" class="logo" />
        <span class="app-title">清风系统</span>
      </div>
    </div>

    <div class="menu-content">
      <ul>
        <li
          v-for="item in menuItems"
          :key="item.name"
          :class="{
            active: props.activeMenu === item.name || (item.children && item.children.some(child => child.name === props.activeMenu)),
            'has-children': item.children,
            expanded: item.children && expandedMenus.includes(item.name)
          }"
          @click="handleMenuClick(item)"
        >
          <div class="menu-item-content">
            <span class="icon">{{ item.icon }}</span>
            <span class="text">{{ item.label }}</span>
            <span v-if="item.children" class="expand-icon">
              {{ expandedMenus.includes(item.name) ? '▼' : '▶' }}
            </span>
          </div>

          <!-- 二级菜单 -->
          <ul v-if="item.children && expandedMenus.includes(item.name)" class="submenu">
            <li
              v-for="child in item.children"
              :key="child.name"
              :class="{ active: props.activeMenu === child.name }"
              @click.stop="activateSubmenu(child.name, item.name)"
            >
              <span class="icon">{{ child.icon }}</span>
              <span class="text">{{ child.label }}</span>
            </li>
          </ul>
        </li>
      </ul>
    </div>

    <div class="menu-footer">
      <div class="footer-info-line">
        <div class="version-time-info">
          <span class="version-info">v{{ appVersion }}</span>
          <span class="time-info">{{ currentTime }}</span>
        </div>
        <div class="debug-controls">
          <button
            v-if="isDev"
            @click="toggleDebugInfo"
            class="debug-btn"
            :class="{ active: showDebugInfo }"
            title="切换调试信息"
          >
            🐛
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, onUnmounted } from 'vue'
import logoImage from '../assets/qingfeng.png'

const props = defineProps({
  activeMenu: {
    type: String,
    required: true
  }
})

const emit = defineEmits(['menu-click', 'toggle-debug'])

const currentTime = ref('')
const currentDate = ref('')
const expandedMenus = ref([]) // 默认折叠所有菜单
const logoSrc = ref(logoImage)
const appVersion = ref('1.0.0')
const isDev = ref(import.meta.env.DEV)
const showDebugInfo = ref(false)

const menuItems = computed(() => [
  { name: 'Workbench', label: '工作台', icon: '💼' },

  {
    name: 'ScriptManagement',
    label: '脚本管理',
    icon: '📁',
    children: [
      { name: 'CmdScripts', label: 'CMD脚本', icon: '📜' },
      { name: 'JsScripts', label: 'JS脚本', icon: '📄' },
      { name: 'PythonScripts', label: 'Python脚本', icon: '🐍' }
    ]
  },
  {
      name: 'ToolsManagement',
      label: '工具管理',
      icon: '🔧',
      children: [
        { name: 'EasyVoiceWebTTS', label: '文本转语音', icon: '🗣️' },
        { name: 'VoiceCloning', label: '语音克隆', icon: '🎭' },
        { name: 'SnakeGame', label: '贪吃蛇游戏', icon: '🐍' }
      ]
    },
  { name: 'ScheduledTasks', label: '定时任务', icon: '⏰' },
  { name: 'Hotkeys', label: '快捷键', icon: '⌨️' },
  { name: 'Settings', label: '设置', icon: '⚙️' }
])

function handleMenuClick(item) {
  if (item.children) {
    // 切换展开/收起状态
    const index = expandedMenus.value.indexOf(item.name)
    if (index > -1) {
      expandedMenus.value.splice(index, 1)
    } else {
      expandedMenus.value.push(item.name)
    }

    // 有子菜单的项目只展开子菜单，不导航到页面
  } else {
    activateMenu(item.name)
  }
}

function activateMenu(name) {
  console.log(`[SidebarMenu] 激活菜单: ${name}`)
  emit('menu-click', name, false, {})
}

function activateSubmenu(childName, parentName) {
  console.log(`[SidebarMenu] 激活子菜单: ${childName}, 父菜单: ${parentName}`)
  console.log(`[SidebarMenu] 准备发出menu-click事件`)
  
  // 直接导航到子菜单页面
  emit('menu-click', childName, false, {})
  
  console.log(`[SidebarMenu] menu-click事件已发出: ${childName}`)
}

function toggleDebugInfo() {
  showDebugInfo.value = !showDebugInfo.value
  emit('toggle-debug', showDebugInfo.value)
  console.log(`[SidebarMenu] 调试信息: ${showDebugInfo.value ? '开启' : '关闭'}`)
}



function updateTime() {
  const now = new Date()
  currentTime.value = now.toLocaleTimeString('zh-CN', {
    hour12: false,
    hour: '2-digit',
    minute: '2-digit',
    second: '2-digit'
  })
  currentDate.value = now.toLocaleDateString('zh-CN', {
    month: '2-digit',
    day: '2-digit'
  })
}

// 获取应用版本号
import { safeApiCall } from '../utils/apiCheck.js'
async function getAppVersion() {
  try {
    // 使用API检查工具安全调用
    const result = await safeApiCall('getAppInfo', { success: true, version: '1.0.0-dev' })
    if (result.success) {
      appVersion.value = result.version
    } else {
      appVersion.value = '1.0.0-dev'
    }
  } catch (error) {
    console.error('Failed to get app version:', error)
    appVersion.value = '1.0.0-dev'
  }
}

let timeInterval = null

onMounted(() => {
  updateTime()
  getAppVersion()
  timeInterval = setInterval(updateTime, 1000)
})

onUnmounted(() => {
  if (timeInterval) {
    clearInterval(timeInterval)
  }
})
</script>

<style scoped>
.sidebar-menu {
  width: 240px;
  background-color: var(--sidebar-background);
  height: 100%;
  display: flex;
  flex-direction: column;
  border-right: 1px solid var(--border);
  transition: background-color var(--transition-normal), border-color var(--transition-normal);
}

.menu-header {
  padding: 16px;
  border-bottom: 1px solid var(--border);
  background-color: var(--sidebar-header-background);
  transition: background-color var(--transition-normal), border-color var(--transition-normal);
}

.logo-section {
  display: flex;
  align-items: center;
  gap: 12px;
}

.logo {
  width: 24px;
  height: 24px;
  object-fit: contain;
  margin-left: 40px;
}

.app-title {
  font-size: 25px;
  font-weight: 600;
  color: var(--text-primary);
  letter-spacing: 0.5px;
  transition: color var(--transition-normal);
}

.menu-content {
  flex: 1;
  padding: 16px 10px;
  overflow-y: auto;
}

.menu-content::-webkit-scrollbar {
  width: 4px;
}

.menu-content::-webkit-scrollbar-track {
  background: transparent;
}

.menu-content::-webkit-scrollbar-thumb {
  background: var(--border);
  border-radius: 2px;
  transition: background-color var(--transition-fast);
}

.menu-content::-webkit-scrollbar-thumb:hover {
  background: var(--border-secondary);
}

ul {
  list-style-type: none;
  padding: 0;
  margin: 0;
}

li {
  margin-bottom: 4px;
  font-size: 15px;
  color: var(--text-tertiary);
  transition: color var(--transition-normal);
}

.menu-item-content {
  display: flex;
  align-items: center;
  padding: 12px 15px;
  border-radius: 8px;
  transition: all var(--transition-fast);
  cursor: pointer;
}

li.active > .menu-item-content {
  background-color: var(--sidebar-item-active);
  color: var(--text-primary);
}

li:not(.active) > .menu-item-content:hover {
  background-color: var(--sidebar-item-hover);
}

li.has-children > .menu-item-content {
  justify-content: space-between;
}

.icon {
  margin-right: 12px;
  font-size: 18px;
}

.text {
  flex: 1;
  font-weight: 500;
}

.expand-icon {
  font-size: 12px;
  transition: transform var(--transition-fast);
}

.submenu {
  margin-top: 4px;
  margin-left: 20px;
  padding-left: 15px;
  border-left: 1px solid var(--border);
  transition: border-color var(--transition-normal);
}

.submenu li {
  padding: 8px 10px;
  border-radius: 6px;
  transition: all var(--transition-fast);
  cursor: pointer;
  display: flex;
  align-items: center;
}

.submenu li .icon {
  font-size: 16px;
}

.submenu li.active {
  background-color: var(--sidebar-item-active);
  color: var(--text-primary);
}

.submenu li:not(.active):hover {
  background-color: var(--sidebar-item-hover);
}

.menu-footer {
  padding: 16px;
  border-top: 1px solid var(--border);
  transition: border-color var(--transition-normal);
  background: linear-gradient(135deg, var(--sidebar-background) 0%, var(--background-secondary) 100%);
}



.footer-info-line {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 10px 12px;
  font-size: 12px;
  color: var(--text-tertiary);
  border-radius: 6px;
  background: var(--card-background);
  border: 1px solid var(--border);
  transition: all var(--transition-normal);
}

.version-time-info {
  display: flex;
  flex-direction: column;
  gap: 2px;
  flex: 1;
}

.version-info {
  font-weight: 600;
  color: var(--primary);
}

.time-info {
  font-family: 'Courier New', monospace;
  color: var(--text-secondary);
  font-size: 11px;
}

.debug-controls {
  display: flex;
  align-items: center;
}

.debug-btn {
  width: 24px;
  height: 24px;
  border: none;
  background: transparent;
  color: var(--text-tertiary);
  border-radius: 4px;
  cursor: pointer;
  transition: all var(--transition-fast);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 12px;
}

.debug-btn:hover {
  background: var(--sidebar-item-hover);
  color: var(--text-primary);
}

.debug-btn.active {
  background: var(--primary);
  color: white;
}
</style>