/**
 * Page State Manager
 * 页面状态管理器 - 实现页面持久化和后台运行功能
 */

import { reactive, ref, markRaw } from 'vue'

class PageStateManager {
  constructor() {
    // 页面实例缓存
    this.pageInstances = reactive(new Map())
    // 页面状态缓存
    this.pageStates = reactive(new Map())
    // 当前活动页面
    this.activePage = ref(null)
    // 页面组件映射
    this.componentMap = new Map()
    // 页面配置
    this.pageConfigs = new Map()
    
    this.initialized = false
  }

  /**
   * 初始化页面状态管理器
   */
  initialize() {
    if (this.initialized) return
    
    // 注册页面组件
    this.registerComponents()
    
    // 加载保存的状态
    this.loadPersistedState()
    
    // 设置自动保存
    this.setupAutoSave()
    
    this.initialized = true
    console.log('[PageStateManager] 初始化完成')
  }

  /**
   * 注册页面组件
   */
  registerComponents() {
    // 脚本管理相关页面
    this.registerPage('CmdScripts', {
      title: 'CMD脚本',
      icon: '💻',
      category: 'script',
      persistent: true,
      keepAlive: true
    })
    
    this.registerPage('PythonScripts', {
      title: 'Python脚本',
      icon: '🐍',
      category: 'script',
      persistent: true,
      keepAlive: true
    })
    
    this.registerPage('JsScripts', {
      title: 'JS脚本',
      icon: '📜',
      category: 'script',
      persistent: true,
      keepAlive: true
    })

    // 工具管理相关页面
    this.registerPage('ToolsManagement', {
      title: '工具管理',
      icon: '🔧',
      category: 'tools',
      persistent: true,
      keepAlive: true
    })

    this.registerPage('ToolsWorkspace', {
      title: '工具工作区',
      icon: '🛠️',
      category: 'tools',
      persistent: true,
      keepAlive: true
    })

    // 其他需要持久化的页面
    this.registerPage('ScheduledTasks', {
      title: '定时任务',
      icon: '⏰',
      category: 'system',
      persistent: true,
      keepAlive: true
    })

    this.registerPage('Settings', {
      title: '设置',
      icon: '⚙️',
      category: 'system',
      persistent: true,
      keepAlive: true
    })

    this.registerPage('Workbench', {
      title: '工作台',
      icon: '🏠',
      category: 'main',
      persistent: true,
      keepAlive: true
    })
  }

  /**
   * 注册页面
   */
  registerPage(pageId, config) {
    this.pageConfigs.set(pageId, {
      id: pageId,
      ...config,
      createdAt: new Date(),
      lastAccessed: null
    })
  }

  /**
   * 创建或获取页面实例
   */
  async getPageInstance(pageId, componentFactory) {
    // 检查是否已有实例
    if (this.pageInstances.has(pageId)) {
      const instance = this.pageInstances.get(pageId)
      // 更新最后访问时间
      this.updateLastAccessed(pageId)
      console.log(`[PageStateManager] 复用页面实例: ${pageId}`)
      return instance
    }

    // 创建新实例
    const config = this.pageConfigs.get(pageId)
    if (!config) {
      console.warn(`[PageStateManager] 未找到页面配置: ${pageId}`)
      return null
    }

    try {
      // 创建组件实例
      const component = await componentFactory()
      const instance = {
        id: pageId,
        component: markRaw(component),
        config,
        state: this.getPageState(pageId),
        createdAt: new Date(),
        lastAccessed: new Date(),
        isActive: false,
        isLoaded: true
      }

      this.pageInstances.set(pageId, instance)
      this.updateLastAccessed(pageId)
      
      console.log(`[PageStateManager] 创建新页面实例: ${pageId}`)
      return instance
    } catch (error) {
      console.error(`[PageStateManager] 创建页面实例失败: ${pageId}`, error)
      return null
    }
  }

  /**
   * 激活页面
   */
  activatePage(pageId) {
    // 取消激活所有页面
    this.pageInstances.forEach(instance => {
      instance.isActive = false
    })

    // 激活指定页面
    const instance = this.pageInstances.get(pageId)
    if (instance) {
      instance.isActive = true
      instance.lastAccessed = new Date()
      this.activePage.value = pageId
      this.updateLastAccessed(pageId)
      
      console.log(`[PageStateManager] 激活页面: ${pageId}`)
    }
  }

  /**
   * 获取页面状态
   */
  getPageState(pageId) {
    if (!this.pageStates.has(pageId)) {
      this.pageStates.set(pageId, reactive({
        scrollPosition: { x: 0, y: 0 },
        formData: {},
        selectedItems: [],
        filters: {},
        searchQuery: '',
        viewMode: 'default',
        expandedItems: [],
        customData: {},
        lastSaved: new Date()
      }))
    }
    return this.pageStates.get(pageId)
  }

  /**
   * 更新页面状态
   */
  updatePageState(pageId, stateUpdate) {
    const state = this.getPageState(pageId)
    Object.assign(state, stateUpdate, { lastSaved: new Date() })
    
    // 触发自动保存
    this.scheduleAutoSave()
  }

  /**
   * 保存页面状态 - 兼容性方法
   */
  savePageState(pageId, stateData) {
    this.updatePageState(pageId, stateData)
    console.log(`[PageStateManager] 保存页面状态: ${pageId}`, stateData)
  }

  /**
   * 保存页面滚动位置
   */
  saveScrollPosition(pageId, scrollElement) {
    if (!scrollElement) return
    
    const state = this.getPageState(pageId)
    state.scrollPosition = {
      x: scrollElement.scrollLeft,
      y: scrollElement.scrollTop
    }
  }

  /**
   * 恢复页面滚动位置
   */
  restoreScrollPosition(pageId, scrollElement) {
    if (!scrollElement) return
    
    const state = this.getPageState(pageId)
    if (state.scrollPosition) {
      scrollElement.scrollLeft = state.scrollPosition.x
      scrollElement.scrollTop = state.scrollPosition.y
    }
  }

  /**
   * 清理页面实例
   */
  cleanupPage(pageId) {
    const instance = this.pageInstances.get(pageId)
    if (!instance) return

    const config = instance.config
    
    // 如果页面不需要持久化，直接删除
    if (!config.persistent) {
      this.pageInstances.delete(pageId)
      this.pageStates.delete(pageId)
      console.log(`[PageStateManager] 清理非持久化页面: ${pageId}`)
      return
    }

    // 持久化页面标记为非活动状态
    instance.isActive = false
    instance.lastAccessed = new Date()
    
    console.log(`[PageStateManager] 页面转入后台: ${pageId}`)
  }

  /**
   * 获取所有活动页面
   */
  getActivePages() {
    return Array.from(this.pageInstances.values()).filter(instance => instance.isActive)
  }

  /**
   * 获取所有后台页面
   */
  getBackgroundPages() {
    return Array.from(this.pageInstances.values()).filter(instance => !instance.isActive && instance.isLoaded)
  }

  /**
   * 更新最后访问时间
   */
  updateLastAccessed(pageId) {
    const config = this.pageConfigs.get(pageId)
    if (config) {
      config.lastAccessed = new Date()
    }
  }

  /**
   * 获取页面统计信息
   */
  getStats() {
    const instances = Array.from(this.pageInstances.values())
    const states = Array.from(this.pageStates.values())
    
    return {
      totalPages: instances.length,
      activePages: instances.filter(i => i.isActive).length,
      backgroundPages: instances.filter(i => !i.isActive && i.isLoaded).length,
      persistentPages: instances.filter(i => i.config.persistent).length,
      totalStates: states.length,
      memoryUsage: this.estimateMemoryUsage(),
      oldestPage: instances.length > 0 ? instances.reduce((oldest, page) => 
        page.createdAt < oldest.createdAt ? page : oldest
      ) : null
    }
  }

  /**
   * 估算内存使用量
   */
  estimateMemoryUsage() {
    try {
      const statesSize = JSON.stringify(Array.from(this.pageStates.values())).length
      const instancesCount = this.pageInstances.size
      
      return {
        statesSize: `${(statesSize / 1024).toFixed(2)} KB`,
        instancesCount,
        estimated: `${((statesSize + instancesCount * 1024) / 1024).toFixed(2)} KB`
      }
    } catch (error) {
      return { error: 'Unable to estimate' }
    }
  }

  /**
   * 设置自动保存
   */
  setupAutoSave() {
    this.autoSaveTimeout = null
    
    // 监听页面卸载事件
    if (typeof window !== 'undefined') {
      window.addEventListener('beforeunload', () => {
        this.savePersistedState()
      })
    }
  }

  /**
   * 计划自动保存
   */
  scheduleAutoSave() {
    if (this.autoSaveTimeout) {
      clearTimeout(this.autoSaveTimeout)
    }
    
    this.autoSaveTimeout = setTimeout(() => {
      this.savePersistedState()
    }, 2000) // 2秒后保存
  }

  /**
   * 保存持久化状态
   */
  savePersistedState() {
    try {
      const persistentStates = {}
      const persistentConfigs = {}
      
      // 只保存持久化页面的状态
      this.pageStates.forEach((state, pageId) => {
        const config = this.pageConfigs.get(pageId)
        if (config && config.persistent) {
          persistentStates[pageId] = {
            ...state,
            lastSaved: state.lastSaved.toISOString()
          }
          persistentConfigs[pageId] = {
            ...config,
            createdAt: config.createdAt.toISOString(),
            lastAccessed: config.lastAccessed ? config.lastAccessed.toISOString() : null
          }
        }
      })

      const stateData = {
        states: persistentStates,
        configs: persistentConfigs,
        activePage: this.activePage.value,
        savedAt: new Date().toISOString(),
        version: '1.0'
      }

      localStorage.setItem('pageStateManager_data', JSON.stringify(stateData))
      console.log(`[PageStateManager] 保存了 ${Object.keys(persistentStates).length} 个页面状态`)
    } catch (error) {
      console.error('[PageStateManager] 保存状态失败:', error)
    }
  }

  /**
   * 加载持久化状态
   */
  loadPersistedState() {
    try {
      const stateJson = localStorage.getItem('pageStateManager_data')
      if (!stateJson) return

      const stateData = JSON.parse(stateJson)
      
      // 恢复页面状态
      if (stateData.states) {
        Object.entries(stateData.states).forEach(([pageId, state]) => {
          const restoredState = {
            ...state,
            lastSaved: new Date(state.lastSaved)
          }
          this.pageStates.set(pageId, reactive(restoredState))
        })
      }

      // 恢复页面配置
      if (stateData.configs) {
        Object.entries(stateData.configs).forEach(([pageId, config]) => {
          const restoredConfig = {
            ...config,
            createdAt: new Date(config.createdAt),
            lastAccessed: config.lastAccessed ? new Date(config.lastAccessed) : null
          }
          this.pageConfigs.set(pageId, restoredConfig)
        })
      }

      // 恢复活动页面
      if (stateData.activePage) {
        this.activePage.value = stateData.activePage
      }

      console.log(`[PageStateManager] 加载了 ${Object.keys(stateData.states || {}).length} 个页面状态`)
    } catch (error) {
      console.error('[PageStateManager] 加载状态失败:', error)
    }
  }

  /**
   * 清理过期状态
   */
  cleanupExpiredStates(maxAge = 7 * 24 * 60 * 60 * 1000) { // 7天
    const now = new Date()
    const expiredPages = []

    this.pageConfigs.forEach((config, pageId) => {
      if (config.lastAccessed && (now - config.lastAccessed) > maxAge) {
        expiredPages.push(pageId)
      }
    })

    expiredPages.forEach(pageId => {
      this.pageInstances.delete(pageId)
      this.pageStates.delete(pageId)
      this.pageConfigs.delete(pageId)
    })

    if (expiredPages.length > 0) {
      console.log(`[PageStateManager] 清理了 ${expiredPages.length} 个过期页面`)
      this.savePersistedState()
    }
  }

  /**
   * 重置所有状态
   */
  reset() {
    this.pageInstances.clear()
    this.pageStates.clear()
    this.pageConfigs.clear()
    this.activePage.value = null
    
    localStorage.removeItem('pageStateManager_data')
    console.log('[PageStateManager] 重置所有状态')
  }
}

// 创建单例实例
export const pageStateManager = new PageStateManager()

// 导出工具函数
export const usePageState = (pageId) => {
  return {
    getState: () => pageStateManager.getPageState(pageId),
    updateState: (update) => pageStateManager.updatePageState(pageId, update),
    saveScrollPosition: (element) => pageStateManager.saveScrollPosition(pageId, element),
    restoreScrollPosition: (element) => pageStateManager.restoreScrollPosition(pageId, element)
  }
}

export default pageStateManager