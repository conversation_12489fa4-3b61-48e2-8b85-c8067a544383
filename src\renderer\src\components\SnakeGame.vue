<template>
  <div class="snake-game-container">
    <div class="game-header">
      <h2>🐍 贪吃蛇游戏</h2>
      <div class="game-info">
        <div class="score-info">
          <span class="score">得分: {{ score }}</span>
          <span class="high-score">最高分: {{ highScore }}</span>
        </div>
        <div class="game-controls">
          <button @click="toggleGame" :class="['control-btn', gameState]">
            {{ gameState === 'playing' ? '⏸️ 暂停' : gameState === 'paused' ? '▶️ 继续' : '🎮 开始游戏' }}
          </button>
          <button @click="resetGame" class="control-btn reset">🔄 重置</button>
          <button @click="decreaseSpeed" class="control-btn speed" :disabled="gameSpeed >= 300">⬇️ 减速</button>
          <button @click="increaseSpeed" class="control-btn speed" :disabled="gameSpeed <= 50">⬆️ 加速</button>
          <button @click="toggleAutoMode" :class="['control-btn', 'auto', { active: autoMode }]">
            {{ autoMode ? '🤖 自动模式' : '👤 手动模式' }}
          </button>
        </div>
      </div>
    </div>

    <div class="game-settings">
      <div class="setting-group">
        <label>游戏速度:</label>
        <select v-model="gameSpeed" @change="updateGameSpeed">
          <option value="200">慢速</option>
          <option value="150">中速</option>
          <option value="100">快速</option>
          <option value="50">极速</option>
        </select>
      </div>
      <div class="setting-group">
        <label>网格大小:</label>
        <select v-model="gridSize" @change="resetGame">
          <option value="15">精细 (15x15)</option>
          <option value="20">小 (20x20)</option>
          <option value="25">中 (25x25)</option>
          <option value="30">大 (30x30)</option>
        </select>
      </div>
    </div>

    <div class="game-board-container">
      <canvas
        ref="gameCanvas"
        :width="canvasSize"
        :height="canvasSize"
        class="game-canvas"
        @keydown="handleKeyPress"
        tabindex="0"
      ></canvas>

      <div v-if="gameState === 'gameOver'" class="game-over-overlay">
        <div class="game-over-content">
          <h3>🎮 游戏结束</h3>
          <p>最终得分: {{ score }}</p>
          <p v-if="score === highScore" class="new-record">🎉 新纪录！</p>
          <button @click="resetGame" class="restart-btn">再来一局</button>
        </div>
      </div>
    </div>

    <div class="game-instructions">
      <h4>🎮 游戏说明</h4>
      <div class="instructions-grid">
        <div class="instruction-item">
          <span class="key">⬆️⬇️⬅️➡️</span>
          <span class="desc">方向键控制移动</span>
        </div>
        <div class="instruction-item">
          <span class="key">空格</span>
          <span class="desc">暂停/继续游戏</span>
        </div>
        <div class="instruction-item">
          <span class="key">🤖</span>
          <span class="desc">自动模式：AI智能寻路</span>
        </div>
        <div class="instruction-item">
          <span class="key">🍎</span>
          <span class="desc">吃食物增长身体</span>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, onUnmounted, nextTick } from 'vue'

// 游戏状态
const gameState = ref('stopped') // 'stopped', 'playing', 'paused', 'gameOver'
const score = ref(0)
const highScore = ref(parseInt(localStorage.getItem('snakeHighScore') || '0'))
const autoMode = ref(true) // 默认开启自动模式
const gameSpeed = ref(50) // 默认极速
const gridSize = ref(15) // 缩小网格尺寸

// 游戏元素
const gameCanvas = ref(null)
const canvasSize = ref(500)
let ctx = null
let gameInterval = null

// 蛇和食物
let snake = [{ x: 10, y: 10 }]
let direction = { x: 0, y: 0 }
let nextDirection = { x: 0, y: 0 }
let food = { x: 15, y: 15 }

// 游戏循环
function gameLoop() {
  if (gameState.value !== 'playing') return

  // 自动模式下使用AI控制
  if (autoMode.value) {
    const aiDirection = findPathToFood()
    if (aiDirection) {
      nextDirection = getDirectionVector(aiDirection)
    }
  }

  // 更新方向（避免反向移动）
  if (nextDirection.x !== 0 || nextDirection.y !== 0) {
    if (!(direction.x === -nextDirection.x && direction.y === -nextDirection.y)) {
      direction = { ...nextDirection }
    }
  }

  // 移动蛇
  const head = { x: snake[0].x + direction.x, y: snake[0].y + direction.y }

  // 检查碰撞
  if (checkCollision(head)) {
    gameOver()
    return
  }

  snake.unshift(head)

  // 检查是否吃到食物
  if (head.x === food.x && head.y === food.y) {
    score.value += 10
    generateFood()

    // 更新最高分
    if (score.value > highScore.value) {
      highScore.value = score.value
      localStorage.setItem('snakeHighScore', highScore.value.toString())
    }
  } else {
    snake.pop()
  }

  draw()
}

// 获取方向向量
function getDirectionVector(directionName) {
  const vectors = {
    'up': { x: 0, y: -1 },
    'down': { x: 0, y: 1 },
    'left': { x: -1, y: 0 },
    'right': { x: 1, y: 0 }
  }
  return vectors[directionName] || { x: 0, y: 0 }
}

// 改进的AI寻路算法 - 使用A*算法和安全性评估
function findPathToFood() {
  const head = snake[0]
  const gridCount = Math.floor(canvasSize.value / gridSize.value)

  // 首先尝试A*寻路到食物
  const pathToFood = findPathAStar(head, food, gridCount)
  
  if (pathToFood && pathToFood.length > 1) {
    // 检查路径是否安全（确保吃到食物后还有逃生路径）
    const nextPos = pathToFood[1]
    const nextDir = getDirectionFromPositions(head, nextPos)
    
    if (isSafeMove(nextDir, gridCount)) {
      // 进一步检查：吃到食物后是否还有足够的空间
      if (hasEscapeRoute(nextPos, gridCount)) {
        return nextDir
      }
    }
  }

  // 如果直接去食物不安全，寻找安全的移动方向
  return findSafestDirection(gridCount)
}

// A*寻路算法实现
function findPathAStar(start, goal, gridCount) {
  const openSet = [{ ...start, g: 0, h: manhattanDistance(start, goal), f: manhattanDistance(start, goal), parent: null }]
  const closedSet = new Set()
  
  while (openSet.length > 0) {
    // 找到f值最小的节点
    openSet.sort((a, b) => a.f - b.f)
    const current = openSet.shift()
    
    // 到达目标
    if (current.x === goal.x && current.y === goal.y) {
      const path = []
      let node = current
      while (node) {
        path.unshift({ x: node.x, y: node.y })
        node = node.parent
      }
      return path
    }
    
    closedSet.add(`${current.x},${current.y}`)
    
    // 检查四个方向的邻居
    const neighbors = [
      { x: current.x + 1, y: current.y },
      { x: current.x - 1, y: current.y },
      { x: current.x, y: current.y + 1 },
      { x: current.x, y: current.y - 1 }
    ]
    
    for (const neighbor of neighbors) {
      // 检查边界和蛇身碰撞
      if (neighbor.x < 0 || neighbor.x >= gridCount || 
          neighbor.y < 0 || neighbor.y >= gridCount ||
          isSnakeBody(neighbor) ||
          closedSet.has(`${neighbor.x},${neighbor.y}`)) {
        continue
      }
      
      const g = current.g + 1
      const h = manhattanDistance(neighbor, goal)
      const f = g + h
      
      const existingNode = openSet.find(node => node.x === neighbor.x && node.y === neighbor.y)
      if (!existingNode || g < existingNode.g) {
        if (existingNode) {
          existingNode.g = g
          existingNode.f = f
          existingNode.parent = current
        } else {
          openSet.push({ ...neighbor, g, h, f, parent: current })
        }
      }
    }
  }
  
  return null // 无路径
}

// 曼哈顿距离
function manhattanDistance(a, b) {
  return Math.abs(a.x - b.x) + Math.abs(a.y - b.y)
}

// 检查是否是蛇身
function isSnakeBody(pos) {
  return snake.some((segment, index) => 
    segment.x === pos.x && segment.y === pos.y && index < snake.length - 1
  )
}

// 根据两个位置获取方向
function getDirectionFromPositions(from, to) {
  const dx = to.x - from.x
  const dy = to.y - from.y
  
  if (dx === 1) return 'right'
  if (dx === -1) return 'left'
  if (dy === 1) return 'down'
  if (dy === -1) return 'up'
  return null
}

// 检查移动是否安全
function isSafeMove(dir, gridCount) {
  const vector = getDirectionVector(dir)
  const newHead = { x: snake[0].x + vector.x, y: snake[0].y + vector.y }

  // 检查边界
  if (newHead.x < 0 || newHead.x >= gridCount || newHead.y < 0 || newHead.y >= gridCount) {
    return false
  }

  // 检查蛇身碰撞（除了尾部）
  for (let i = 0; i < snake.length - 1; i++) {
    if (snake[i].x === newHead.x && snake[i].y === newHead.y) {
      return false
    }
  }

  // 检查是否反向移动
  const currentDir = getCurrentDirection()
  const opposites = { 'up': 'down', 'down': 'up', 'left': 'right', 'right': 'left' }
  return dir !== opposites[currentDir]
}

// 检查是否有逃生路径 - 改进版本
function hasEscapeRoute(pos, gridCount) {
  // 使用洪水填充算法检查可达空间
  const visited = new Set()
  const queue = [pos]
  let reachableSpaces = 0

  while (queue.length > 0 && reachableSpaces < snake.length + 5) {
    const current = queue.shift()
    const key = `${current.x},${current.y}`

    if (visited.has(key)) continue
    visited.add(key)
    reachableSpaces++

    const directions = [
      { x: current.x + 1, y: current.y },
      { x: current.x - 1, y: current.y },
      { x: current.x, y: current.y + 1 },
      { x: current.x, y: current.y - 1 }
    ]

    for (const next of directions) {
      if (next.x >= 0 && next.x < gridCount &&
          next.y >= 0 && next.y < gridCount &&
          !isSnakeBody(next) &&
          !visited.has(`${next.x},${next.y}`)) {
        queue.push(next)
      }
    }
  }

  // 确保有足够的空间容纳蛇的长度
  return reachableSpaces >= snake.length + 3
}

// 寻找最安全的方向
function findSafestDirection(gridCount) {
  const directions = ['up', 'down', 'left', 'right']
  const safeDirections = []
  
  for (const dir of directions) {
    if (isSafeMove(dir, gridCount)) {
      const vector = getDirectionVector(dir)
      const newPos = { x: snake[0].x + vector.x, y: snake[0].y + vector.y }
      
      // 计算安全性评分
      const safetyScore = calculateSafetyScore(newPos, gridCount)
      safeDirections.push({ direction: dir, score: safetyScore })
    }
  }
  
  if (safeDirections.length === 0) return null
  
  // 选择安全性评分最高的方向
  safeDirections.sort((a, b) => b.score - a.score)
  return safeDirections[0].direction
}

// 计算位置的安全性评分 - 改进版本
function calculateSafetyScore(pos, gridCount) {
  let score = 0

  // 1. 距离边界的距离（越远越安全）
  const distanceFromBorder = Math.min(pos.x, pos.y, gridCount - 1 - pos.x, gridCount - 1 - pos.y)
  score += distanceFromBorder * 15

  // 2. 使用洪水填充算法计算可达空间
  const reachableSpaces = countReachableSpaces(pos, gridCount)
  score += reachableSpaces * 8

  // 3. 避免靠近蛇头附近的危险区域
  const head = snake[0]
  const distanceFromHead = manhattanDistance(pos, head)
  if (distanceFromHead <= 2) {
    score -= 50 // 严重惩罚靠近蛇头的位置
  }

  // 4. 距离蛇尾的距离（跟随尾部策略，但不要太近）
  const tail = snake[snake.length - 1]
  const distanceToTail = manhattanDistance(pos, tail)
  if (distanceToTail > 1 && distanceToTail <= 4) {
    score += distanceToTail * 5
  }

  // 5. 检查前方几步是否安全
  const futureSteps = checkFutureSafety(pos, gridCount, 3)
  score += futureSteps * 10

  // 6. 距离食物的距离（适度考虑）
  const distanceToFood = manhattanDistance(pos, food)
  score += Math.max(0, 10 - distanceToFood) * 2

  return score
}

// 计算从指定位置可达的空间数量
function countReachableSpaces(startPos, gridCount) {
  const visited = new Set()
  const queue = [startPos]
  let count = 0

  while (queue.length > 0 && count < 50) { // 限制搜索范围
    const current = queue.shift()
    const key = `${current.x},${current.y}`

    if (visited.has(key)) continue
    visited.add(key)
    count++

    const neighbors = [
      { x: current.x + 1, y: current.y },
      { x: current.x - 1, y: current.y },
      { x: current.x, y: current.y + 1 },
      { x: current.x, y: current.y - 1 }
    ]

    for (const neighbor of neighbors) {
      if (neighbor.x >= 0 && neighbor.x < gridCount &&
          neighbor.y >= 0 && neighbor.y < gridCount &&
          !isSnakeBody(neighbor) &&
          !visited.has(`${neighbor.x},${neighbor.y}`)) {
        queue.push(neighbor)
      }
    }
  }

  return count
}

// 检查未来几步的安全性
function checkFutureSafety(pos, gridCount, steps) {
  let safeSteps = 0
  const directions = [
    { x: 1, y: 0 }, { x: -1, y: 0 }, { x: 0, y: 1 }, { x: 0, y: -1 }
  ]

  for (const dir of directions) {
    let currentPos = { ...pos }
    let stepCount = 0

    for (let i = 0; i < steps; i++) {
      currentPos = { x: currentPos.x + dir.x, y: currentPos.y + dir.y }

      if (currentPos.x >= 0 && currentPos.x < gridCount &&
          currentPos.y >= 0 && currentPos.y < gridCount &&
          !isSnakeBody(currentPos)) {
        stepCount++
      } else {
        break
      }
    }

    safeSteps += stepCount
  }

  return safeSteps
}

// 获取当前方向
function getCurrentDirection() {
  if (direction.x === 0 && direction.y === -1) return 'up'
  if (direction.x === 0 && direction.y === 1) return 'down'
  if (direction.x === -1 && direction.y === 0) return 'left'
  if (direction.x === 1 && direction.y === 0) return 'right'
  return null
}

// 检查碰撞
function checkCollision(head) {
  const gridCount = Math.floor(canvasSize.value / gridSize.value)

  // 检查边界碰撞
  if (head.x < 0 || head.x >= gridCount || head.y < 0 || head.y >= gridCount) {
    return true
  }

  // 检查自身碰撞
  for (const segment of snake) {
    if (head.x === segment.x && head.y === segment.y) {
      return true
    }
  }

  return false
}

// 生成食物
function generateFood() {
  const gridCount = Math.floor(canvasSize.value / gridSize.value)
  let newFood

  do {
    newFood = {
      x: Math.floor(Math.random() * gridCount),
      y: Math.floor(Math.random() * gridCount)
    }
  } while (snake.some(segment => segment.x === newFood.x && segment.y === newFood.y))

  food = newFood
}

// 绘制游戏
function draw() {
  if (!ctx) return

  // 清空画布
  ctx.fillStyle = '#1a1a1a'
  ctx.fillRect(0, 0, canvasSize.value, canvasSize.value)

  // 绘制网格
  ctx.strokeStyle = '#333'
  ctx.lineWidth = 1
  for (let i = 0; i <= canvasSize.value; i += gridSize.value) {
    ctx.beginPath()
    ctx.moveTo(i, 0)
    ctx.lineTo(i, canvasSize.value)
    ctx.stroke()

    ctx.beginPath()
    ctx.moveTo(0, i)
    ctx.lineTo(canvasSize.value, i)
    ctx.stroke()
  }

  // 绘制蛇
  snake.forEach((segment, index) => {
    ctx.fillStyle = index === 0 ? '#4CAF50' : '#81C784'
    ctx.fillRect(
      segment.x * gridSize.value + 1,
      segment.y * gridSize.value + 1,
      gridSize.value - 2,
      gridSize.value - 2
    )
  })

  // 绘制食物
  ctx.fillStyle = '#F44336'
  ctx.fillRect(
    food.x * gridSize.value + 1,
    food.y * gridSize.value + 1,
    gridSize.value - 2,
    gridSize.value - 2
  )
}

// 游戏控制
function toggleGame() {
  if (gameState.value === 'stopped' || gameState.value === 'gameOver') {
    startGame()
  } else if (gameState.value === 'playing') {
    pauseGame()
  } else if (gameState.value === 'paused') {
    resumeGame()
  }
}

function startGame() {
  resetGameData()
  gameState.value = 'playing'
  gameInterval = setInterval(gameLoop, gameSpeed.value)
}

function pauseGame() {
  gameState.value = 'paused'
  if (gameInterval) {
    clearInterval(gameInterval)
    gameInterval = null
  }
}

function resumeGame() {
  gameState.value = 'playing'
  gameInterval = setInterval(gameLoop, gameSpeed.value)
}

function resetGame() {
  if (gameInterval) {
    clearInterval(gameInterval)
    gameInterval = null
  }
  gameState.value = 'stopped'
  resetGameData()
  draw()
}

function resetGameData() {
  snake = [{ x: 10, y: 10 }]
  direction = { x: 0, y: 0 }
  nextDirection = { x: 0, y: 0 }
  score.value = 0
  generateFood()
}

function gameOver() {
  gameState.value = 'gameOver'
  if (gameInterval) {
    clearInterval(gameInterval)
    gameInterval = null
  }
}

function toggleAutoMode() {
  autoMode.value = !autoMode.value
}

function updateGameSpeed() {
  if (gameInterval && gameState.value === 'playing') {
    clearInterval(gameInterval)
    gameInterval = setInterval(gameLoop, gameSpeed.value)
  }
}

function increaseSpeed() {
  if (gameSpeed.value > 50) {
    gameSpeed.value = Math.max(50, gameSpeed.value - 25)
    updateGameSpeed()
  }
}

function decreaseSpeed() {
  if (gameSpeed.value < 300) {
    gameSpeed.value = Math.min(300, gameSpeed.value + 25)
    updateGameSpeed()
  }
}

// 键盘控制
function handleKeyPress(event) {
  if (gameState.value !== 'playing' || autoMode.value) return

  switch (event.key) {
    case 'ArrowUp':
      nextDirection = { x: 0, y: -1 }
      break
    case 'ArrowDown':
      nextDirection = { x: 0, y: 1 }
      break
    case 'ArrowLeft':
      nextDirection = { x: -1, y: 0 }
      break
    case 'ArrowRight':
      nextDirection = { x: 1, y: 0 }
      break
    case ' ':
      event.preventDefault()
      toggleGame()
      break
  }
}

// 生命周期
onMounted(async () => {
  await nextTick()
  if (gameCanvas.value) {
    ctx = gameCanvas.value.getContext('2d')
    resetGameData()
    draw()

    // 聚焦画布以接收键盘事件
    gameCanvas.value.focus()
  }
})

onUnmounted(() => {
  if (gameInterval) {
    clearInterval(gameInterval)
  }
})
</script>

<style scoped>
.snake-game-container {
  padding: 20px;
  max-width: 800px;
  margin: 0 auto;
  background: var(--background-primary);
  color: var(--text-primary);
}

.game-header {
  text-align: center;
  margin-bottom: 20px;
}

.game-header h2 {
  margin: 0 0 15px 0;
  color: var(--primary);
}

.game-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-wrap: wrap;
  gap: 15px;
}

.score-info {
  display: flex;
  gap: 20px;
  font-weight: 600;
}

.score {
  color: var(--success);
}

.high-score {
  color: var(--warning);
}

.game-controls {
  display: flex;
  gap: 10px;
  flex-wrap: wrap;
}

.control-btn {
  padding: 8px 16px;
  border: none;
  border-radius: 6px;
  cursor: pointer;
  font-weight: 500;
  transition: all 0.3s ease;
}

.control-btn.stopped,
.control-btn.gameOver {
  background: var(--success);
  color: white;
}

.control-btn.playing {
  background: var(--warning);
  color: white;
}

.control-btn.paused {
  background: var(--primary);
  color: white;
}

.control-btn.reset {
  background: var(--danger);
  color: white;
}

.control-btn.auto {
  background: var(--background-secondary);
  color: var(--text-primary);
  border: 2px solid var(--border);
}

.control-btn.auto.active {
  background: var(--info);
  color: white;
  border-color: var(--info);
}

.control-btn.speed {
  background: var(--background-tertiary);
  color: var(--text-primary);
  border: 1px solid var(--border);
  font-size: 12px;
  padding: 6px 12px;
}

.control-btn.speed:hover:not(:disabled) {
  background: var(--primary);
  color: white;
}

.control-btn.speed:disabled {
  opacity: 0.5;
  cursor: not-allowed;
  transform: none;
}

.control-btn:hover:not(:disabled) {
  opacity: 0.8;
  transform: translateY(-1px);
}

.game-settings {
  display: flex;
  justify-content: center;
  gap: 30px;
  margin-bottom: 20px;
  flex-wrap: wrap;
}

.setting-group {
  display: flex;
  align-items: center;
  gap: 8px;
}

.setting-group label {
  font-weight: 500;
  color: var(--text-secondary);
}

.setting-group select {
  padding: 5px 10px;
  border: 1px solid var(--border);
  border-radius: 4px;
  background: var(--background-secondary);
  color: var(--text-primary);
}

.game-board-container {
  position: relative;
  display: flex;
  justify-content: center;
  margin-bottom: 20px;
}

.game-canvas {
  border: 2px solid var(--border);
  border-radius: 8px;
  background: #1a1a1a;
  cursor: pointer;
}

.game-canvas:focus {
  outline: 2px solid var(--primary);
  outline-offset: 2px;
}

.game-over-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.8);
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 8px;
}

.game-over-content {
  background: var(--background-secondary);
  padding: 30px;
  border-radius: 12px;
  text-align: center;
  border: 2px solid var(--border);
}

.game-over-content h3 {
  margin: 0 0 15px 0;
  color: var(--danger);
}

.game-over-content p {
  margin: 10px 0;
  font-size: 16px;
}

.new-record {
  color: var(--warning);
  font-weight: 600;
}

.restart-btn {
  margin-top: 15px;
  padding: 10px 20px;
  background: var(--success);
  color: white;
  border: none;
  border-radius: 6px;
  cursor: pointer;
  font-weight: 500;
  transition: all 0.3s ease;
}

.restart-btn:hover {
  opacity: 0.8;
  transform: translateY(-1px);
}

.game-instructions {
  background: var(--background-secondary);
  padding: 20px;
  border-radius: 8px;
  border: 1px solid var(--border);
}

.game-instructions h4 {
  margin: 0 0 15px 0;
  color: var(--primary);
}

.instructions-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 15px;
}

.instruction-item {
  display: flex;
  align-items: center;
  gap: 10px;
}

.instruction-item .key {
  background: var(--background-tertiary);
  padding: 4px 8px;
  border-radius: 4px;
  font-weight: 600;
  min-width: 60px;
  text-align: center;
  border: 1px solid var(--border);
}

.instruction-item .desc {
  color: var(--text-secondary);
  font-size: 14px;
}

@media (max-width: 768px) {
  .game-info {
    flex-direction: column;
    text-align: center;
  }

  .game-settings {
    flex-direction: column;
    align-items: center;
  }

  .instructions-grid {
    grid-template-columns: 1fr;
  }
}
</style>